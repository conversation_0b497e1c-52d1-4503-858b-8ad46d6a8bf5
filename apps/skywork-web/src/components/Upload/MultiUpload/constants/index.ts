import Dropbox from "@/components/Upload/MultiUpload/components/tab/Dropbox.vue";
import ExpertData from "@/components/Upload/MultiUpload/components/tab/ExpertData/index.vue";
import GoogleDrive from "@/components/Upload/MultiUpload/components/tab/GoogleDrive.vue";
import Home from "@/components/Upload/MultiUpload/components/tab/Home/index.vue";
import Knowledge from "@/components/Upload/MultiUpload/components/tab/Knowledge.vue";
import LinkPaste from "@/components/Upload/MultiUpload/components/tab/LinkPaste/index.vue";
import Local from "@/components/Upload/MultiUpload/components/tab/Local.vue";
import PPTSelect from "@/components/Upload/MultiUpload/components/tab/PPTSelect/index.vue";
import PPTUpload from "@/components/Upload/MultiUpload/components/tab/PPTUpload.vue";
import TextPaste from "@/components/Upload/MultiUpload/components/tab/TextPaste/index.vue";
import { Component } from "vue";

interface Detail {
  title: string;
  components: Component;
  tips?: string;
}
export const COMPONENT_MAP: Record<string, Detail> = {
  home: { title: "上传", components: Home, tips: "这是 tips" },
  pptSelect: { title: "选择PPT模版", components: PPTSelect },
  pptUpload: { title: "上传PPT模版", components: PPTUpload },
  local: { title: "本地上传", components: Local },
  knowledge: { title: "知识库", components: Knowledge },
  linkPaste: { title: "粘贴链接", components: LinkPaste },
  textPaste: { title: "粘贴文本", components: TextPaste },
  googleDrive: { title: "Google Drive", components: GoogleDrive },
  dropbox: { title: "Dropbox", components: Dropbox },
  expertData: { title: "专业数据", components: ExpertData },
};
